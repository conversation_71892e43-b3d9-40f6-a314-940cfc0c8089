# Design Decisions

## Core Architecture

### Streaming JSON Processing
Use streaming JSON parsing with struson crate instead of loading entire files into memory.
- Traditional: O(file_size) memory usage
- Streaming: O(row_size) memory usage

### Language: Rust
Implement in Rust using DuckDB's C API bindings.
- User preference for Rust development
- Memory safety for database extensions
- Strong JSON parsing ecosystem (struson)

## API Design

### Automatic Path Inference
Implement automatic JSON path inference based on query column patterns rather than explicit path parameters.
- Enables `SELECT * FROM streaming_json_reader('file.json')`
- Leverages DuckDB's projection pushdown

### Context Preservation for Nested Structures
Maintain parent object context when flattening nested arrays.
Follow DuckDB's `select a, unnest(b)` pattern where `a` values repeat for each unnested row of `b`.

### SELECT * Behavior
`SELECT *` should return exactly the same as DuckDB's default JSON reader.

### Error Handling
Fail-fast on malformed JSON rather than attempting partial recovery.
- Simpler error handling logic
- Clear failure modes for debugging

### No Hard-coded JSON Structure Assumptions
Extension should work with any JSON structure, not just specific schemas. Make it truly generic.

## Query-Driven Structure Inference

JSON structure should be inferred from query structure. For example, `unnest(x)` should assume x is a nested field and unnest it into rows. Research needed on DuckDB's extension interface capabilities.

## Memory Management

Use adaptive batching based on DuckDB's vector sizes. Prefer low memory usage when choosing between options.

## Development Workflow

- Use uv for Python package management
- Make incremental git commits
- Design for general, reusable package
- Consult user when multiple design options exist
- Organize test code in separate folders
- Document design decisions for future reference

## Implementation Status

### Schema Discovery
Use first element schema only, with query-driven column selection.
- Avoids performance penalty of full file scanning
- Users can define schema by selecting named fields
- Enables streaming without lookahead

### Type System Integration
Work towards full DuckDB JSON type compatibility. Use VARCHAR for intermediate versions.
- Should eventually match DuckDB's existing JSON reader API
- Full types including nested types is the end goal

### API Surface
Single parameter API: `streaming_json_reader(file_path)` with query-driven behavior.
- `SELECT *` should match DuckDB's `read_json_auto()` behavior exactly
- Context-preserving flattening via query structure
- Query planner integration determines behavior

## Current Status

### Working Features
- Projection pushdown (skips unrequested fields)
- Error handling for malformed JSON
- Basic JSON parsing and STRUCT creation
- String data types

### Critical Issues
- Numbers stored as VARCHAR instead of DOUBLE/INTEGER
- Extension crashes on deep nesting (2+ levels)
- STRUCT field values have wrong data types
- Array processing untested due to crashes




