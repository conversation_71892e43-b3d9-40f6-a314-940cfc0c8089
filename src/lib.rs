extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    error::Error,
    ffi::CString,
    fs::File,
    io::BufReader,
    path::Path,
    sync::atomic::{AtomicBool, AtomicUsize, Ordering},
};
use struson::reader::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sonStreamReader};

/// Represents a column index with potential nested field access
#[derive(Debug, Clone)]
struct ColumnIndex {
    index: usize,
    child_indexes: Vec<ColumnIndex>,
}

impl ColumnIndex {
    fn new(index: usize) -> Self {
        Self {
            index,
            child_indexes: Vec::new(),
        }
    }

    fn with_children(index: usize, children: Vec<ColumnIndex>) -> Self {
        Self {
            index,
            child_indexes: children,
        }
    }

    fn has_children(&self) -> bool {
        !self.child_indexes.is_empty()
    }

    fn get_primary_index(&self) -> usize {
        self.index
    }

    fn get_child_indexes(&self) -> &[ColumnIndex] {
        &self.child_indexes
    }
}

/// Represents a JSON value type with proper recursive semantics
#[derive(Debug, Clone, PartialEq)]
enum JsonValueType {
    Null,
    Boolean,
    Number,
    String,
    Array(Box<JsonValueType>),      // Array of elements of this type
    Object(Vec<JsonField>),         // Object with named fields
}

/// Represents a field in a JSON object
#[derive(Debug, Clone, PartialEq)]
struct JsonField {
    name: String,
    value_type: JsonValueType,
}

/// Represents a path through the JSON structure
#[derive(Debug, Clone, PartialEq)]
enum JsonPathSegment {
    Field(String),                  // Access object field by name
    ArrayIndex(usize),              // Access specific array index
    ArrayWildcard,                  // Flatten array into rows
}

/// Complete path to a value in JSON structure
#[derive(Debug, Clone)]
struct JsonPath {
    segments: Vec<JsonPathSegment>,
}

impl JsonPath {
    fn root() -> Self {
        Self { segments: vec![] }
    }

    fn field(name: &str) -> Self {
        Self { segments: vec![JsonPathSegment::Field(name.to_string())] }
    }

    fn extend_field(&self, name: &str) -> Self {
        let mut segments = self.segments.clone();
        segments.push(JsonPathSegment::Field(name.to_string()));
        Self { segments }
    }

    fn extend_array_wildcard(&self) -> Self {
        let mut segments = self.segments.clone();
        segments.push(JsonPathSegment::ArrayWildcard);
        Self { segments }
    }

    fn to_string(&self) -> String {
        let mut result = String::new();
        for (i, segment) in self.segments.iter().enumerate() {
            if i > 0 { result.push('.'); }
            match segment {
                JsonPathSegment::Field(name) => result.push_str(name),
                JsonPathSegment::ArrayIndex(idx) => result.push_str(&format!("[{}]", idx)),
                JsonPathSegment::ArrayWildcard => result.push_str("[*]"),
            }
        }
        result
    }
}

/// Represents the discovered JSON schema with recursive structure
#[derive(Debug, Clone)]
struct JsonSchema {
    root_type: JsonValueType,
    columns: Vec<StructuredColumn>,
}

/// Represents a column with proper structured types (STRUCT/ARRAY)
#[derive(Debug, Clone)]
struct StructuredColumn {
    name: String,
    value_type: JsonValueType,
}

/// Query projection information mapped to JSON paths
#[derive(Debug, Clone)]
struct JsonProjection {
    required_paths: Vec<JsonPath>,
    column_mapping: Vec<(usize, JsonPath)>, // (column_index, json_path)
}

#[repr(C)]
struct JsonReaderBindData {
    file_path: String,
    schema: JsonSchema,
}

#[repr(C)]
struct JsonReaderInitData {
    current_element: AtomicUsize,
    finished: AtomicBool,
    projected_columns: Vec<ColumnIndex>, // Columns requested by the query
}

struct JsonReaderVTab;

// Helper function to discover JSON schema with proper recursive analysis
fn discover_json_schema(
    file_path: &str,
    projected_columns: Option<&[ColumnIndex]>
) -> Result<JsonSchema, Box<dyn std::error::Error>> {
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    // Analyze the root JSON structure recursively
    let root_type = analyze_json_value(&mut json_reader, &JsonPath::root())?;

    // Generate structured columns based on the discovered structure
    let columns = if let Some(projected) = projected_columns {
        // Query-driven: only generate columns for projected fields
        generate_projected_columns(&root_type, projected)?
    } else {
        // Discovery mode: generate all possible columns
        generate_all_columns(&root_type)?
    };

    if columns.is_empty() {
        return Err("No suitable JSON structure found for table representation".into());
    }

    Ok(JsonSchema {
        root_type,
        columns,
    })
}

// Recursively analyze JSON structure to build proper type representation
fn analyze_json_value(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    _current_path: &JsonPath
) -> Result<JsonValueType, Box<dyn std::error::Error>> {
    match json_reader.peek()? {
        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            Ok(JsonValueType::Null)
        }
        struson::reader::ValueType::Boolean => {
            json_reader.next_bool()?;
            Ok(JsonValueType::Boolean)
        }
        struson::reader::ValueType::Number => {
            json_reader.next_number_as_str()?;
            Ok(JsonValueType::Number)
        }
        struson::reader::ValueType::String => {
            json_reader.next_string()?;
            Ok(JsonValueType::String)
        }
        struson::reader::ValueType::Array => {
            json_reader.begin_array()?;

            // Analyze first element to determine array element type
            let element_type = if json_reader.has_next()? {
                let first_element_type = analyze_json_value(json_reader, _current_path)?;

                // Skip remaining elements for now (we could analyze more for union types)
                while json_reader.has_next()? {
                    json_reader.skip_value()?;
                }

                first_element_type
            } else {
                // Empty array - assume string elements
                JsonValueType::String
            };

            json_reader.end_array()?;
            Ok(JsonValueType::Array(Box::new(element_type)))
        }
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;
            let mut fields = Vec::new();

            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                let field_path = _current_path.extend_field(&field_name);
                let field_type = analyze_json_value(json_reader, &field_path)?;

                fields.push(JsonField {
                    name: field_name,
                    value_type: field_type,
                });
            }

            json_reader.end_object()?;
            Ok(JsonValueType::Object(fields))
        }
    }
}

// Generate columns for projected fields only
fn generate_projected_columns(
    _root_type: &JsonValueType,
    _projected: &[ColumnIndex]
) -> Result<Vec<StructuredColumn>, Box<dyn std::error::Error>> {
    // TODO: Implement query-driven column generation
    Err("Query-driven column generation not yet implemented".into())
}

// Generate structured columns from JSON structure (preserving hierarchy)
fn generate_all_columns(
    root_type: &JsonValueType
) -> Result<Vec<StructuredColumn>, Box<dyn std::error::Error>> {
    match root_type {
        JsonValueType::Object(fields) => {
            // Root object: each field becomes a top-level column
            let mut columns = Vec::new();
            for field in fields {
                columns.push(StructuredColumn {
                    name: field.name.clone(),
                    value_type: field.value_type.clone(),
                });
            }
            Ok(columns)
        }
        JsonValueType::Array(element_type) => {
            // Root array: create a single column containing the array
            Ok(vec![StructuredColumn {
                name: "value".to_string(),
                value_type: root_type.clone(),
            }])
        }
        _ => {
            // Root primitive: single column
            Ok(vec![StructuredColumn {
                name: "value".to_string(),
                value_type: root_type.clone(),
            }])
        }
    }
}

// Convert JsonValueType to DuckDB LogicalTypeHandle
fn json_type_to_duckdb_type(json_type: &JsonValueType) -> Result<LogicalTypeHandle, Box<dyn std::error::Error>> {
    match json_type {
        JsonValueType::Boolean => Ok(LogicalTypeHandle::from(LogicalTypeId::Boolean)),
        JsonValueType::Number => Ok(LogicalTypeHandle::from(LogicalTypeId::Double)), // Use proper Double type for numbers
        JsonValueType::String => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonValueType::Null => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonValueType::Array(element_type) => {
            // Create proper LIST type with correct element type
            let element_logical_type = json_type_to_duckdb_type(element_type)?;
            Ok(LogicalTypeHandle::list(&element_logical_type))
        }
        JsonValueType::Object(fields) => {
            // Create proper STRUCT type for nested objects
            let mut struct_fields = Vec::new();

            for field in fields {
                let field_type = json_type_to_duckdb_type(&field.value_type)?;
                struct_fields.push((field.name.as_str(), field_type));
            }

            Ok(LogicalTypeHandle::struct_type(&struct_fields))
        }
    }
}

// Apply projection pushdown to only include requested columns
fn apply_projection_pushdown(
    full_schema: &JsonSchema,
    projected_column_names: &[String],
) -> JsonSchema {
    eprintln!("DEBUG PROJECTION: Applying pushdown for columns: {:?}", projected_column_names);

    // If no specific projection, return full schema
    if projected_column_names.is_empty() {
        eprintln!("DEBUG PROJECTION: No projection specified, using full schema");
        return full_schema.clone();
    }

    // Filter columns to only include projected ones
    let mut projected_columns = Vec::new();

    for column_name in projected_column_names {
        if let Some(column) = full_schema.columns.iter().find(|col| &col.name == column_name) {
            eprintln!("DEBUG PROJECTION: Including column: {}", column_name);
            projected_columns.push(column.clone());
        } else {
            eprintln!("DEBUG PROJECTION: Warning - requested column '{}' not found in schema", column_name);
        }
    }

    JsonSchema {
        root_type: full_schema.root_type.clone(),
        columns: projected_columns,
    }
}

// Read JSON file using streaming parser for memory efficiency with comprehensive error handling
fn read_json_as_structured_data(
    file_path: &str,
    schema: &JsonSchema,
) -> Result<Vec<Vec<serde_json::Value>>, Box<dyn std::error::Error>> {
    use std::fs::File;
    use struson::reader::{JsonReader, JsonStreamReader};

    eprintln!("DEBUG STREAMING: Opening file for streaming JSON parsing: {}", file_path);

    // Validate file exists and is readable
    if !std::path::Path::new(file_path).exists() {
        return Err(format!("JSON file not found: {}", file_path).into());
    }

    // Open file for streaming with error handling
    let file = match File::open(file_path) {
        Ok(f) => f,
        Err(e) => return Err(format!("Failed to open JSON file '{}': {}", file_path, e).into()),
    };

    let mut json_reader = JsonStreamReader::new(file);
    let mut rows = Vec::new();

    // Start reading the JSON structure with comprehensive error handling
    match json_reader.peek() {
        Ok(value_type) => match value_type {
            struson::reader::ValueType::Object => {
                eprintln!("DEBUG STREAMING: Processing single JSON object");
                match read_object_streaming(&mut json_reader, schema) {
                    Ok(row) => rows.push(row),
                    Err(e) => return Err(format!("Error parsing JSON object: {}", e).into()),
                }
            }
            struson::reader::ValueType::Array => {
                eprintln!("DEBUG STREAMING: Processing JSON array");
                match json_reader.begin_array() {
                    Ok(_) => {
                        let mut element_count = 0;
                        while json_reader.has_next().unwrap_or(false) {
                            element_count += 1;
                            if element_count > 10000 {
                                eprintln!("DEBUG STREAMING: Warning - processing large array with {} elements", element_count);
                            }

                            match json_reader.peek() {
                                Ok(struson::reader::ValueType::Object) => {
                                    match read_object_streaming(&mut json_reader, schema) {
                                        Ok(row) => rows.push(row),
                                        Err(e) => {
                                            eprintln!("DEBUG STREAMING: Warning - skipping malformed object at element {}: {}", element_count, e);
                                            // Try to skip the malformed element
                                            if let Err(skip_err) = json_reader.skip_value() {
                                                return Err(format!("Failed to skip malformed element: {}", skip_err).into());
                                            }
                                        }
                                    }
                                }
                                Ok(_) => {
                                    // Skip non-object elements with warning
                                    eprintln!("DEBUG STREAMING: Warning - skipping non-object element at position {}", element_count);
                                    if let Err(e) = json_reader.skip_value() {
                                        return Err(format!("Failed to skip non-object element: {}", e).into());
                                    }
                                }
                                Err(e) => {
                                    return Err(format!("Error reading array element {}: {}", element_count, e).into());
                                }
                            }
                        }

                        if let Err(e) = json_reader.end_array() {
                            return Err(format!("Error closing JSON array: {}", e).into());
                        }
                    }
                    Err(e) => return Err(format!("Error starting JSON array: {}", e).into()),
                }
            }
            _ => {
                return Err(format!("Unsupported JSON root type: expected object or array, found {:?}", value_type).into());
            }
        },
        Err(e) => {
            return Err(format!("Failed to read JSON file '{}': {} (file may be empty or malformed)", file_path, e).into());
        }
    }

    eprintln!("DEBUG STREAMING: Successfully parsed {} rows using streaming", rows.len());

    if rows.is_empty() {
        eprintln!("DEBUG STREAMING: Warning - no valid data rows found in JSON file");
    }

    Ok(rows)
}

// Read a JSON object using streaming parser with projection optimization and error handling
fn read_object_streaming(
    json_reader: &mut JsonStreamReader<File>,
    schema: &JsonSchema,
) -> Result<Vec<serde_json::Value>, Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    let mut row = Vec::new();

    // Initialize row with nulls for all columns
    for _ in &schema.columns {
        row.push(serde_json::Value::Null);
    }

    // Begin object with error handling
    if let Err(e) = json_reader.begin_object() {
        return Err(format!("Failed to start reading JSON object: {}", e).into());
    }

    let mut field_count = 0;
    while json_reader.has_next().unwrap_or(false) {
        field_count += 1;

        // Prevent infinite loops on malformed JSON
        if field_count > 1000 {
            return Err("JSON object has too many fields (>1000), possible malformed JSON".into());
        }

        let field_name = match json_reader.next_name() {
            Ok(name) => name.to_string(),
            Err(e) => return Err(format!("Failed to read field name at position {}: {}", field_count, e).into()),
        };

        // Find the column index for this field
        if let Some(col_idx) = schema.columns.iter().position(|col| col.name == field_name) {
            // Only parse fields that are in our schema (projection pushdown)
            eprintln!("DEBUG PROJECTION: Parsing required field: {}", field_name);
            match read_value_streaming(json_reader) {
                Ok(value) => row[col_idx] = value,
                Err(e) => {
                    eprintln!("DEBUG ERROR: Failed to parse field '{}': {}", field_name, e);
                    // Continue with null value for this field
                    row[col_idx] = serde_json::Value::Null;
                    // Try to skip the problematic value
                    if let Err(skip_err) = json_reader.skip_value() {
                        return Err(format!("Failed to skip malformed field '{}': {}", field_name, skip_err).into());
                    }
                }
            }
        } else {
            // Skip fields not in schema (projection optimization)
            eprintln!("DEBUG PROJECTION: Skipping unrequested field: {}", field_name);
            if let Err(e) = json_reader.skip_value() {
                return Err(format!("Failed to skip unrequested field '{}': {}", field_name, e).into());
            }
        }
    }

    // End object with error handling
    if let Err(e) = json_reader.end_object() {
        return Err(format!("Failed to close JSON object: {}", e).into());
    }

    Ok(row)
}

// Read a JSON value using streaming parser and convert to serde_json::Value
fn read_value_streaming(
    json_reader: &mut JsonStreamReader<File>,
) -> Result<serde_json::Value, Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader, ValueType};

    match json_reader.peek()? {
        ValueType::Null => {
            json_reader.next_null()?;
            Ok(serde_json::Value::Null)
        }
        ValueType::Boolean => {
            let b = json_reader.next_bool()?;
            Ok(serde_json::Value::Bool(b))
        }
        ValueType::Number => {
            // Parse number properly using struson API
            let number_str = json_reader.next_number_as_str()?;
            eprintln!("DEBUG STREAMING: Parsing number: {}", number_str);
            // Convert to f64 and then to serde_json::Value
            let number_value: f64 = number_str.parse().unwrap_or(0.0);
            Ok(serde_json::json!(number_value))
        }
        ValueType::String => {
            let s = json_reader.next_string()?;
            Ok(serde_json::Value::String(s))
        }
        ValueType::Array => {
            let mut arr = Vec::new();
            json_reader.begin_array()?;

            while json_reader.has_next()? {
                let value = read_value_streaming(json_reader)?;
                arr.push(value);
            }

            json_reader.end_array()?;
            Ok(serde_json::Value::Array(arr))
        }
        ValueType::Object => {
            let mut obj = serde_json::Map::new();
            json_reader.begin_object()?;

            while json_reader.has_next()? {
                let key = json_reader.next_name()?.to_string();
                let value = read_value_streaming(json_reader)?;
                obj.insert(key, value);
            }

            json_reader.end_object()?;
            Ok(serde_json::Value::Object(obj))
        }
    }
}

// Insert a structured value into a DuckDB vector with depth protection
fn insert_structured_value(
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    value: &serde_json::Value,
    column_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    insert_structured_value_with_depth(output, col_idx, row_idx, value, column_type, 0)
}

// Insert a structured value with depth tracking to prevent stack overflow
fn insert_structured_value_with_depth(
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    value: &serde_json::Value,
    column_type: &JsonValueType,
    depth: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    // Prevent infinite recursion and stack overflow
    if depth > 15 {
        eprintln!("DEBUG INSERT: Maximum nesting depth exceeded ({}), converting to JSON string", depth);
        // Convert deeply nested structures to JSON strings to prevent stack overflow
        let json_str = match serde_json::to_string(value) {
            Ok(s) => s,
            Err(_) => "null".to_string(),
        };
        let vector = output.flat_vector(col_idx);
        let cstring = match std::ffi::CString::new(json_str) {
            Ok(s) => s,
            Err(_) => match std::ffi::CString::new("null") {
                Ok(s) => s,
                Err(_) => unsafe { std::ffi::CString::from_vec_unchecked(vec![b'n', b'u', b'l', b'l', 0]) }
            }
        };
        vector.insert(row_idx, cstring);
        return Ok(());
    }

    eprintln!("DEBUG INSERT: Inserting value type {:?} into column {} (depth: {})", column_type, col_idx, depth);

    match column_type {
        JsonValueType::Object(fields) => {
            // Handle STRUCT type with depth protection
            eprintln!("DEBUG INSERT: Handling STRUCT with {} fields at depth {}", fields.len(), depth);

            // For deeply nested structures, convert to JSON string to prevent stack overflow and panics
            // Use a conservative threshold to prevent DuckDB STRUCT creation issues
            if depth > 2 {
                eprintln!("DEBUG INSERT: Deep nesting detected at depth {}, converting STRUCT to JSON string", depth);
                let json_str = match serde_json::to_string(value) {
                    Ok(s) => s,
                    Err(_) => "{}".to_string(),
                };
                let vector = output.flat_vector(col_idx);
                let cstring = match std::ffi::CString::new(json_str) {
                    Ok(s) => s,
                    Err(_) => match std::ffi::CString::new("{}") {
                        Ok(s) => s,
                        Err(_) => unsafe { std::ffi::CString::from_vec_unchecked(vec![b'{', b'}', 0]) }
                    }
                };
                vector.insert(row_idx, cstring);
                return Ok(());
            }

            let mut struct_vector = output.struct_vector(col_idx);

            if let serde_json::Value::Object(obj) = value {
                eprintln!("DEBUG INSERT: Processing object with {} fields", obj.len());
                // Insert each field into the corresponding child vector
                for (field_idx, field) in fields.iter().enumerate() {
                    eprintln!("DEBUG INSERT: Processing field {} ({}) at depth {}", field_idx, field.name, depth);
                    let field_value = obj.get(&field.name).unwrap_or(&serde_json::Value::Null);
                    let mut child_vector = struct_vector.child(field_idx, 1);
                    // Use panic-free insertion for STRUCT fields
                    if let Err(e) = insert_primitive_value_with_depth(&mut child_vector, 0, field_value, &field.value_type, depth + 1) {
                        eprintln!("DEBUG INSERT: Error inserting STRUCT field {}: {}", field.name, e);
                        // Insert null value as fallback to prevent crashes
                        child_vector.set_null(0);
                    }
                }
            } else if value.is_null() {
                eprintln!("DEBUG INSERT: Setting STRUCT as null");
                struct_vector.set_null(row_idx);
            }
        }
        JsonValueType::Array(element_type) => {
            // Handle LIST type with proper structured insertion
            eprintln!("DEBUG INSERT: Handling ARRAY with element type: {:?}", element_type);
            let mut list_vector = output.list_vector(col_idx);

            if let serde_json::Value::Array(arr) = value {
                eprintln!("DEBUG INSERT: Processing array with {} elements", arr.len());

                // Set up the list entry - this tells DuckDB how many elements are in this list
                list_vector.set_entry(row_idx, 0, arr.len());

                // Get the child vector to insert array elements
                let mut child_vector = list_vector.child(arr.len());

                // Insert each array element into the child vector
                for (elem_idx, elem) in arr.iter().enumerate() {
                    eprintln!("DEBUG INSERT: Inserting array element {} with value: {:?}", elem_idx, elem);

                    // For complex types (STRUCT/ARRAY), we need special handling
                    match element_type.as_ref() {
                        JsonValueType::Object(fields) => {
                            // For STRUCT elements in arrays, we need to handle this properly
                            eprintln!("DEBUG INSERT: Inserting STRUCT element {} with {} fields", elem_idx, fields.len());

                            if let serde_json::Value::Object(obj) = elem {
                                // The child_vector should be a STRUCT vector for STRUCT elements
                                // We need to access it as a StructVector and insert into its child vectors

                                // The key insight: the child_vector from list_vector.child() should already be
                                // the correct type based on the schema. For STRUCT elements, it should be a StructVector.

                                // However, the DuckDB Rust API might not expose this directly.
                                // Let's try a different approach: use the child_vector directly but treat it as structured data

                                // For each field in the STRUCT, we need to insert into the appropriate child vector
                                // But since we don't have direct access to StructVector methods on child_vector,
                                // we'll need to work around this limitation

                                // Let's try to understand what methods are available on child_vector
                                // The child_vector should be a STRUCT vector for STRUCT elements
                                eprintln!("DEBUG INSERT: Attempting to access child_vector as StructVector");

                                // Let's try to access STRUCT-specific methods
                                // Maybe the child_vector has different methods available
                                eprintln!("DEBUG INSERT: child_vector type: {:?}", std::any::type_name_of_val(&child_vector));

                                // For now, let's implement a simplified version that works with the available API
                                eprintln!("DEBUG INSERT: STRUCT in array not yet fully implemented, using JSON fallback");
                                let json_str = serde_json::to_string(elem)?;
                                let cstring = std::ffi::CString::new(json_str)?;
                                child_vector.insert(elem_idx, cstring);
                            } else if elem.is_null() {
                                eprintln!("DEBUG INSERT: STRUCT element is null, setting as null");
                                child_vector.set_null(elem_idx);
                            } else {
                                eprintln!("DEBUG INSERT: STRUCT element is not an object, converting to JSON string");
                                let json_str = serde_json::to_string(elem)?;
                                let cstring = std::ffi::CString::new(json_str)?;
                                child_vector.insert(elem_idx, cstring);
                            }
                        }
                        JsonValueType::Array(_) => {
                            // For nested arrays, convert to JSON string for now
                            // TODO: Implement proper nested ARRAY handling
                            eprintln!("DEBUG INSERT: Converting nested ARRAY to JSON string");
                            let json_str = serde_json::to_string(elem)?;
                            let cstring = std::ffi::CString::new(json_str)?;
                            child_vector.insert(elem_idx, cstring);
                        }
                        _ => {
                            // For primitive types, use the existing function
                            insert_primitive_value(&mut child_vector, elem_idx, elem, element_type)?;
                        }
                    }
                }
                eprintln!("DEBUG INSERT: Successfully inserted all array elements");
            } else if value.is_null() {
                eprintln!("DEBUG INSERT: Setting ARRAY as null");
                list_vector.set_null(row_idx);
            } else {
                eprintln!("DEBUG INSERT: Warning - expected array but got: {:?}", value);
                list_vector.set_null(row_idx);
            }
        }
        _ => {
            // Handle primitive types
            let mut vector = output.flat_vector(col_idx);
            insert_primitive_value(&mut vector, row_idx, value, column_type)?;
        }
    }
    Ok(())
}

// Insert a value into a vector (can be primitive or structured) with depth protection
fn insert_primitive_value(
    vector: &mut duckdb::core::FlatVector,
    row_idx: usize,
    value: &serde_json::Value,
    value_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    insert_primitive_value_with_depth(vector, row_idx, value, value_type, 0)
}

// Insert a value with depth tracking to prevent stack overflow and panics
fn insert_primitive_value_with_depth(
    vector: &mut duckdb::core::FlatVector,
    row_idx: usize,
    value: &serde_json::Value,
    value_type: &JsonValueType,
    depth: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    // Prevent infinite recursion and stack overflow
    if depth > 20 {
        eprintln!("DEBUG PRIMITIVE: Maximum nesting depth exceeded ({}), converting to JSON string", depth);
        let json_str = match serde_json::to_string(value) {
            Ok(s) => s,
            Err(_) => "null".to_string(),
        };
        let cstring = match std::ffi::CString::new(json_str) {
            Ok(s) => s,
            Err(_) => match std::ffi::CString::new("null") {
                Ok(s) => s,
                Err(_) => unsafe { std::ffi::CString::from_vec_unchecked(vec![b'n', b'u', b'l', b'l', 0]) }
            }
        };
        vector.insert(row_idx, cstring);
        return Ok(());
    }

    eprintln!("DEBUG PRIMITIVE: Inserting value type {:?} at row {} (depth: {})", value_type, row_idx, depth);

    match value_type {
        JsonValueType::String => {
            let s = value.as_str().unwrap_or("null");
            let cstring = match std::ffi::CString::new(s) {
                Ok(s) => s,
                Err(_) => match std::ffi::CString::new("null") {
                    Ok(s) => s,
                    Err(_) => unsafe { std::ffi::CString::from_vec_unchecked(vec![b'n', b'u', b'l', b'l', 0]) }
                }
            };
            vector.insert(row_idx, cstring);
        }
        JsonValueType::Number => {
            let n = value.as_f64().unwrap_or(0.0);
            eprintln!("DEBUG PRIMITIVE: Inserting number value: {}", n);

            // The DuckDB Rust API FlatVector doesn't implement Inserter<f64>
            // We need to use direct memory access via as_mut_slice
            let data_slice: &mut [f64] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = n;
            eprintln!("DEBUG PRIMITIVE: Successfully inserted f64 value {} at index {}", n, row_idx);
        }
        JsonValueType::Boolean => {
            let b = value.as_bool().unwrap_or(false);
            let bool_str = b.to_string();
            let cstring = match std::ffi::CString::new(bool_str) {
                Ok(s) => s,
                Err(_) => match std::ffi::CString::new("false") {
                    Ok(s) => s,
                    Err(_) => unsafe { std::ffi::CString::from_vec_unchecked(vec![b'f', b'a', b'l', b's', b'e', 0]) }
                }
            };
            vector.insert(row_idx, cstring);
        }
        JsonValueType::Null => {
            // Set the vector element as NULL instead of inserting the string "null"
            vector.set_null(row_idx);
            eprintln!("DEBUG PRIMITIVE: Set NULL value at index {}", row_idx);
        }
        JsonValueType::Object(fields) => {
            // Handle STRUCT type within array with depth protection
            eprintln!("DEBUG PRIMITIVE: Handling nested STRUCT with {} fields at depth {}", fields.len(), depth);

            // For ANY nested objects in primitive context, convert to JSON string to prevent panics
            // This is because we're in a primitive insertion context, not a structured context
            eprintln!("DEBUG PRIMITIVE: Converting nested STRUCT to JSON string (primitive context)");

            // Convert to JSON string with completely panic-free error handling
            let value_str = match serde_json::to_string(value) {
                Ok(s) => s,
                Err(_) => "{}".to_string(),
            };

            // Create CString with multiple fallback levels - absolutely no panics allowed
            let cstring = match std::ffi::CString::new(value_str) {
                Ok(s) => s,
                Err(_) => {
                    // JSON string contained null bytes, try safe fallback
                    match std::ffi::CString::new("{}") {
                        Ok(s) => s,
                        Err(_) => {
                            // Even "{}" failed, use minimal safe fallback
                            match std::ffi::CString::new("null") {
                                Ok(s) => s,
                                Err(_) => {
                                    // Last resort - create manually to guarantee success
                                    unsafe { std::ffi::CString::from_vec_unchecked(vec![b'0', 0]) }
                                }
                            }
                        }
                    }
                }
            };
            vector.insert(row_idx, cstring);
        }
        JsonValueType::Array(_) => {
            // Handle nested arrays with depth protection
            eprintln!("DEBUG PRIMITIVE: Handling nested ARRAY at depth {}", depth);

            // For deeply nested arrays, always convert to JSON string to prevent panics
            if depth > 5 {
                eprintln!("DEBUG PRIMITIVE: Deep nesting detected, converting ARRAY to JSON string");
            }

            // Convert to JSON string with panic-free error handling
            let value_str = match serde_json::to_string(value) {
                Ok(s) => s,
                Err(_) => "[]".to_string(),
            };

            // Create CString with panic-free fallbacks
            let cstring = match std::ffi::CString::new(value_str) {
                Ok(s) => s,
                Err(_) => {
                    match std::ffi::CString::new("[]") {
                        Ok(s) => s,
                        Err(_) => {
                            // Last resort fallback
                            unsafe { std::ffi::CString::from_vec_unchecked(vec![b'[', b']', 0]) }
                        }
                    }
                }
            };
            vector.insert(row_idx, cstring);
        }
    }
    Ok(())
}

// Helper function to read and flatten JSON arrays generically
fn read_and_flatten_json(
    file_path: &str,
    schema: &JsonSchema,
    init_data: &JsonReaderInitData
) -> Result<Vec<Vec<String>>, Box<dyn std::error::Error>> {
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // For the first call, read the entire array and return first batch
    let current_element = init_data.current_element.load(Ordering::Relaxed);

    if current_element > 0 {
        // We've already processed some elements, return empty to indicate we're done
        // TODO: Implement proper streaming across multiple calls
        return Ok(vec![]);
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    let columns = &schema.columns;
    let mut result_columns: Vec<Vec<String>> = vec![Vec::new(); columns.len()];
    let mut elements_read = 0;
    let max_elements = 100; // Limit to prevent memory issues

    match json_reader.peek()? {
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;

            // Look for the first array field
            while json_reader.has_next()? {
                let _field_name = json_reader.next_name()?;

                if let struson::reader::ValueType::Array = json_reader.peek()? {
                    json_reader.begin_array()?;

                    // Process array elements
                    while json_reader.has_next()? && elements_read < max_elements {
                        if let struson::reader::ValueType::Object = json_reader.peek()? {
                            json_reader.begin_object()?;
                            let mut row_data = vec!["".to_string(); columns.len()];

                            while json_reader.has_next()? {
                                let field_name = json_reader.next_name()?;
                                if let Some(col_idx) = columns.iter().position(|c| c.name == field_name) {
                                    // Extract the value for this column
                                    let value = match json_reader.peek()? {
                                        struson::reader::ValueType::String => json_reader.next_string()?,
                                        struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                        struson::reader::ValueType::Boolean => json_reader.next_bool()?.to_string(),
                                        struson::reader::ValueType::Null => {
                                            json_reader.next_null()?;
                                            "null".to_string()
                                        }
                                        _ => {
                                            json_reader.skip_value()?;
                                            "".to_string()
                                        }
                                    };
                                    row_data[col_idx] = value;
                                } else {
                                    json_reader.skip_value()?;
                                }
                            }
                            json_reader.end_object()?;

                            // Add row data to result columns
                            for (col_idx, value) in row_data.into_iter().enumerate() {
                                result_columns[col_idx].push(value);
                            }
                            elements_read += 1;
                        } else {
                            json_reader.skip_value()?;
                            elements_read += 1;
                        }
                    }

                    break; // Found our array, stop looking
                } else {
                    json_reader.skip_value()?;
                }
            }
        }
        struson::reader::ValueType::Array => {
            // Direct array at root level
            json_reader.begin_array()?;

            while json_reader.has_next()? && elements_read < max_elements {
                if let struson::reader::ValueType::Object = json_reader.peek()? {
                    json_reader.begin_object()?;
                    let mut row_data = vec!["".to_string(); columns.len()];

                    while json_reader.has_next()? {
                        let field_name = json_reader.next_name()?;
                        if let Some(col_idx) = columns.iter().position(|c| c.name == field_name) {
                            // Extract the value for this column
                            let value = match json_reader.peek()? {
                                struson::reader::ValueType::String => json_reader.next_string()?,
                                struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                struson::reader::ValueType::Boolean => json_reader.next_bool()?.to_string(),
                                struson::reader::ValueType::Null => {
                                    json_reader.next_null()?;
                                    "null".to_string()
                                }
                                _ => {
                                    json_reader.skip_value()?;
                                    "".to_string()
                                }
                            };
                            row_data[col_idx] = value;
                        } else {
                            json_reader.skip_value()?;
                        }
                    }
                    json_reader.end_object()?;

                    // Add row data to result columns
                    for (col_idx, value) in row_data.into_iter().enumerate() {
                        result_columns[col_idx].push(value);
                    }
                    elements_read += 1;
                } else {
                    json_reader.skip_value()?;
                    elements_read += 1;
                }
            }
        }
        _ => {
            return Err("Expected JSON object or array at root".into());
        }
    }

    // Mark as finished since we read everything in one go
    init_data.finished.store(true, Ordering::Relaxed);
    init_data.current_element.store(elements_read, Ordering::Relaxed);

    Ok(result_columns)
}





impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        eprintln!("DEBUG BIND: File path: {}", file_path);
        eprintln!("DEBUG BIND: Parameter count: {}", bind.get_parameter_count());

        // Try to explore what other information might be available in bind phase
        eprintln!("DEBUG BIND: Exploring BindInfo for additional projection information...");

        // Let's see if BindInfo has any methods that might give us nested field information
        // This is exploratory to understand what's available
        eprintln!("DEBUG BIND: Checking for nested field projection capabilities...");

        // Discover JSON schema from the file (no projection info available at bind time)
        let full_schema = match discover_json_schema(&file_path, None) {
            Ok(schema) => {
                eprintln!("DEBUG BIND: Discovered full schema: {:?}", schema);
                schema
            },
            Err(e) => {
                eprintln!("DEBUG BIND: Schema discovery failed: {}", e);
                return Err(e);
            }
        };

        // At bind time, we don't have projection info yet, so use full schema
        // Projection optimization will be applied during data reading
        eprintln!("DEBUG BIND: Using full schema at bind time (projection applied later)");

        // Add result columns to DuckDB based on full schema
        for (i, column) in full_schema.columns.iter().enumerate() {
            eprintln!("DEBUG BIND: Adding column '{}' at index {}", column.name, i);

            // Convert JSON types to DuckDB logical types (including STRUCT/ARRAY)
            let logical_type = json_type_to_duckdb_type(&column.value_type)?;

            bind.add_result_column(&column.name, logical_type);
            eprintln!("DEBUG BIND: Added '{}' as {:?} type", column.name, column.value_type);
        }

        Ok(JsonReaderBindData {
            file_path,
            schema: full_schema,
        })
    }

    fn init(init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        // Test projection pushdown capabilities
        eprintln!("DEBUG: Testing projection pushdown capabilities");

        // Get column indices for projection pushdown
        let column_indices = init.get_column_indices();
        eprintln!("DEBUG: Projected column indices: {:?}", column_indices);
        eprintln!("DEBUG: Number of projected columns: {}", column_indices.len());

        // Get bind data to access column names
        let bind_data = unsafe { &*(init.get_bind_data() as *const JsonReaderBindData) };
        let column_names: Vec<String> = bind_data.schema.columns.iter().map(|c| c.name.clone()).collect();
        eprintln!("DEBUG: All available columns: {:?}", column_names);

        // Map indices to actual column names
        let projected_column_names: Vec<String> = column_indices
            .iter()
            .map(|&idx| {
                if (idx as usize) < column_names.len() {
                    column_names[idx as usize].clone()
                } else {
                    format!("UNKNOWN_COLUMN_{}", idx)
                }
            })
            .collect();

        eprintln!("DEBUG: Projected column names: {:?}", projected_column_names);

        // Try to explore what other methods might be available on InitInfo
        eprintln!("DEBUG: Exploring InitInfo methods...");

        // Let's try to see if there are any other methods we can call on InitInfo
        // This is exploratory - we'll see what's available

        // Check if there's any way to get more detailed projection information
        eprintln!("DEBUG: Checking for additional projection information...");

        // Let's see if there are any other methods we can call
        // (This is exploratory - some might not exist)

        if column_indices.is_empty() {
            eprintln!("DEBUG: No specific columns projected (SELECT * query?)");
        } else {
            eprintln!("DEBUG: Specific columns requested: {:?}", projected_column_names);
        }

        // Convert column indices to our ColumnIndex structure
        let projected_columns: Vec<ColumnIndex> = column_indices
            .iter()
            .map(|&idx| ColumnIndex::new(idx as usize))
            .collect();

        Ok(JsonReaderInitData {
            current_element: AtomicUsize::new(0),
            finished: AtomicBool::new(false),
            projected_columns,
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = unsafe { &*(func.get_bind_data() as *const JsonReaderBindData) };

        if init_data.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        eprintln!("DEBUG FUNC: Processing file: {}", bind_data.file_path);
        let column_names: Vec<String> = bind_data.schema.columns.iter().map(|c| c.name.clone()).collect();
        eprintln!("DEBUG FUNC: Available columns: {:?}", column_names);

        // Apply projection optimization based on projected columns
        let projected_column_names: Vec<String> = init_data.projected_columns
            .iter()
            .map(|col_idx| {
                if col_idx.index < column_names.len() {
                    column_names[col_idx.index].clone()
                } else {
                    format!("UNKNOWN_COLUMN_{}", col_idx.index)
                }
            })
            .collect();

        eprintln!("DEBUG FUNC: Projected columns for optimization: {:?}", projected_column_names);

        // Apply projection pushdown to schema
        let optimized_schema = apply_projection_pushdown(&bind_data.schema, &projected_column_names);
        eprintln!("DEBUG FUNC: Using optimized schema: {:?}", optimized_schema);

        // Read JSON file and create structured data with projection optimization
        eprintln!("DEBUG FUNC: Reading JSON file for structured data insertion");

        match read_json_as_structured_data(&bind_data.file_path, &optimized_schema) {
            Ok(rows) => {
                eprintln!("DEBUG FUNC: Successfully read {} rows", rows.len());

                if rows.is_empty() {
                    output.set_len(0);
                } else {
                    // Insert structured data into DuckDB vectors
                    eprintln!("DEBUG FUNC: Starting data insertion for {} rows", rows.len());
                    for (row_idx, row_data) in rows.iter().enumerate() {
                        eprintln!("DEBUG FUNC: Processing row {}", row_idx);
                        for (col_idx, value) in row_data.iter().enumerate() {
                            if col_idx < bind_data.schema.columns.len() {
                                eprintln!("DEBUG FUNC: Inserting column {} with value: {:?}", col_idx, value);
                                let column_type = &bind_data.schema.columns[col_idx].value_type;
                                match insert_structured_value(output, col_idx, row_idx, value, column_type) {
                                    Ok(_) => eprintln!("DEBUG FUNC: Successfully inserted column {}", col_idx),
                                    Err(e) => {
                                        eprintln!("DEBUG FUNC: Error inserting column {}: {}", col_idx, e);
                                        return Err(e);
                                    }
                                }
                            }
                        }
                    }
                    output.set_len(rows.len());
                    eprintln!("DEBUG FUNC: Inserted {} rows with structured data", rows.len());

                    // Mark as finished after processing the data
                    init_data.finished.store(true, Ordering::Relaxed);
                    eprintln!("DEBUG FUNC: Marked processing as finished");
                }
            }
            Err(e) => {
                eprintln!("ERROR: Failed to read JSON file '{}': {}", bind_data.file_path, e);

                // Provide helpful error messages based on error type
                let error_msg = if e.to_string().contains("not found") {
                    format!("JSON file not found: {}", bind_data.file_path)
                } else if e.to_string().contains("malformed") || e.to_string().contains("Failed to read") {
                    format!("Malformed JSON in file: {} - {}", bind_data.file_path, e)
                } else if e.to_string().contains("permission") || e.to_string().contains("Permission denied") {
                    format!("Permission denied reading file: {}", bind_data.file_path)
                } else if e.to_string().contains("too many fields") {
                    format!("JSON object too complex in file: {} - {}", bind_data.file_path, e)
                } else {
                    format!("Error reading JSON file '{}': {}", bind_data.file_path, e)
                };

                output.set_len(0);
                init_data.finished.store(true, Ordering::Relaxed);
                eprintln!("DEBUG FUNC: Marked processing as finished due to error");
                return Err(error_msg.into());
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}